package com.longsheng.tools.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.longsheng.tools.common.tenant.TenantContextHolder;
import com.longsheng.tools.common.utils.PageUtils;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.entity.QuotaChangeRecord;
import com.longsheng.tools.system.entity.UserArticleUsage;
import com.longsheng.tools.system.mapper.UserArticleUsageMapper;
import com.longsheng.tools.system.service.QuotaChangeRecordService;
import com.longsheng.tools.system.service.UserArticleUsageService;
import com.longsheng.tools.system.vo.PageVO;
import com.longsheng.tools.system.vo.TableInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 用户文章使用量Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-20
 */
@Service
public class UserArticleUsageServiceImpl extends ServiceImpl<UserArticleUsageMapper, UserArticleUsage> implements UserArticleUsageService {

    @Resource
    private QuotaChangeRecordService quotaChangeRecordService;

    @Override
    public TableInfo list(UserArticleUsage userArticleUsage, int isExport) {
        LambdaQueryWrapper<UserArticleUsage> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (userArticleUsage.getTenantId() != null) {
            queryWrapper.eq(UserArticleUsage::getTenantId, userArticleUsage.getTenantId());
        }

        if (TenantContextHolder.isTenantUser()) {
            queryWrapper.eq(UserArticleUsage::getSysUserId, StpUtil.getLoginIdAsLong());
        }

        // 默认排序
        queryWrapper.orderByDesc(UserArticleUsage::getCreateTime);
        PageVO pageVO = PageUtils.getPageVO();
        // 分页查询
        Page<UserArticleUsage> page = new Page<>(pageVO.getPageNum(), pageVO.getPageSize());
        Page<UserArticleUsage> pageResult = this.page(page, queryWrapper);

        return TableInfo.ok(pageResult);
    }

    @Override
    public RES getUserArticleUsage(Long id) {
        UserArticleUsage userArticleUsage = this.getById(id);
        return RES.ok(userArticleUsage);
    }

    @Override
    public UserArticleUsage userArticleUsage(Long sysUserId) {
        LambdaQueryWrapper<UserArticleUsage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserArticleUsage::getSysUserId, sysUserId);

        List<UserArticleUsage> list = this.list(queryWrapper);

        // 如果不存在，则创建一个新的使用量记录
        if (list.isEmpty()) {
            // 调用专门的创建方法，确保在可写事务中执行
            return createUserArticleUsage(sysUserId);
        }

        return list.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserArticleUsage createUserArticleUsage(Long sysUserId) {
        // 再次检查是否存在，防止并发情况下重复创建
        LambdaQueryWrapper<UserArticleUsage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserArticleUsage::getSysUserId, sysUserId);
        List<UserArticleUsage> list = this.list(queryWrapper);
        if (!list.isEmpty()) {
            return list.get(0);
        }

        // 创建新的使用量记录
        UserArticleUsage usage = new UserArticleUsage();
        usage.setSysUserId(sysUserId);
        usage.setTenantId(TenantContextHolder.getTenantId()); // 设置租户ID，这里使用固定值2，与错误信息中的一致
        usage.setTotalArticlesGenerated(0);
        usage.setTotalArticlesAllowed(0);
        usage.setLastResetDate(new Date());
        usage.setCreateTime(new Date());
        usage.setUpdateTime(new Date());
        this.save(usage);
        return usage;
    }

    @Override
    public RES add(UserArticleUsage userArticleUsage) {
        // 检查是否已存在该用户的使用量记录
        LambdaQueryWrapper<UserArticleUsage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserArticleUsage::getSysUserId, userArticleUsage.getSysUserId());

        long count = this.count(queryWrapper);
        if (count > 0) {
            return RES.no("该用户已存在使用量记录");
        }

        // 设置默认值
        userArticleUsage.setCreateTime(new Date());
        userArticleUsage.setUpdateTime(new Date());

        // 如果未设置已生成文章数，默认为0
        if (userArticleUsage.getTotalArticlesGenerated() == null) {
            userArticleUsage.setTotalArticlesGenerated(0);
        }

        // 如果未设置允许生成文章数，默认为0
        if (userArticleUsage.getTotalArticlesAllowed() == null) {
            userArticleUsage.setTotalArticlesAllowed(0);
        }

        // 如果未设置最后重置日期，默认为当前时间
        if (userArticleUsage.getLastResetDate() == null) {
            userArticleUsage.setLastResetDate(new Date());
        }

        this.save(userArticleUsage);
        return RES.ok();
    }

    @Override
    public RES update(UserArticleUsage userArticleUsage) {
        userArticleUsage.setUpdateTime(new Date());
        this.updateById(userArticleUsage);
        return RES.ok();
    }

    @Override
    public RES delete(Long[] ids) {
        this.removeByIds(Arrays.asList(ids));
        return RES.ok();
    }

    @Override
    public boolean checkArticleQuota(Long sysUserId, int count) {
        // 获取用户的文章使用量
        UserArticleUsage usage = userArticleUsage(sysUserId);

        // 检查是否有足够的配额
        return usage.getTotalArticlesGenerated() + count <= usage.getTotalArticlesAllowed();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RES incrementArticleCount(Long sysUserId, int count) {
        // 获取用户的文章使用量，如果不存在会创建一个新的
        UserArticleUsage usage = userArticleUsage(sysUserId);

        // 增加已生成文章数
        usage.setTotalArticlesGenerated(usage.getTotalArticlesGenerated() + count);
        usage.setUpdateTime(new Date());

        this.updateById(usage);
        return RES.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RES incrementArticleAllowance(Long sysUserId, int count) {
        // 获取用户的文章使用量，如果不存在会创建一个新的
        UserArticleUsage usage = userArticleUsage(sysUserId);
        long currentUserId = StpUtil.getLoginIdAsLong();
        if (currentUserId == sysUserId) {
            return RES.no("无权限分配配额");
        }
        Long tenantId = TenantContextHolder.getTenantId();
        if (TenantContextHolder.isTenantAdmin()){
            //校验租户配额
            UserArticleUsage tenantUsage = userArticleUsage(sysUserId);
            if(tenantUsage.getTotalArticlesAllowed() + count < tenantUsage.getTotalArticlesGenerated()){
                return RES.no("租户配额不足,请先充值");
            }
            //扣减商户配额
            tenantUsage.setTotalArticlesAllowed(tenantUsage.getTotalArticlesAllowed() - count);
            tenantUsage.setUpdateTime(new Date());
            this.updateById(tenantUsage);

            //保存租户配额记录
            QuotaChangeRecord tenantQuotaChangeRecord = new QuotaChangeRecord();
            tenantQuotaChangeRecord.setCreateTime(new Date());
            tenantQuotaChangeRecord.setCount(count * -1);
            tenantQuotaChangeRecord.setAllocationMethod(2);
            tenantQuotaChangeRecord.setUserId((int) currentUserId);
            tenantQuotaChangeRecord.setCreateUser(String.valueOf(currentUserId));
            tenantQuotaChangeRecord.setToUser(sysUserId.intValue());
            tenantQuotaChangeRecord.setTenantId(tenantId);
            quotaChangeRecordService.save(tenantQuotaChangeRecord);

            //保存用户配额记录
            QuotaChangeRecord quotaChangeRecord = new QuotaChangeRecord();
            quotaChangeRecord.setCreateTime(new Date());
            quotaChangeRecord.setCount(count);
            quotaChangeRecord.setAllocationMethod(0);
            quotaChangeRecord.setUserId(sysUserId.intValue());
            quotaChangeRecord.setCreateUser(String.valueOf(currentUserId));
            quotaChangeRecord.setTenantId(tenantId);
            quotaChangeRecordService.save(quotaChangeRecord);

        }
        // 增加允许生成文章数
        usage.setTotalArticlesAllowed(usage.getTotalArticlesAllowed() + count);
        usage.setUpdateTime(new Date());

        this.updateById(usage);
        return RES.ok();
    }
}
